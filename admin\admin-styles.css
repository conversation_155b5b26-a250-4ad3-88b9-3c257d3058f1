.admin-login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;
}

.admin-login-container {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

.admin-login-box {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.admin-login-box h1 {
    margin-bottom: 2rem;
    text-align: center;
    font-family: 'Playfair Display', serif;
}

.admin-login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.admin-login-btn {
    width: 100%;
    padding: 0.875rem;
    background: #000;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.admin-login-btn:hover {
    background: #333;
}

/* Enhanced Admin Layout */
.admin-page {
    min-height: 100vh;
    background: #f1f5f9;
}

.admin-container {
    display: flex;
    min-height: calc(100vh - 60px);
}

.admin-sidebar {
    width: 250px;
    /* Background and padding handled by admin-sidebar.php include */
    border-right: 1px solid #e2e8f0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.admin-main {
    flex: 1;
    padding: 2rem;
    overflow-x: auto;
    background: #f1f5f9;
}

/* Admin Header */
.admin-header {
    background: white;
    padding: 1rem 2rem;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-nav {
    display: flex;
    gap: 2rem;
}

/* Enhanced Admin Content Header */
.admin-content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.admin-content-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.admin-content-header h1 i {
    color: #3b82f6;
    font-size: 1.5rem;
}

/* Product thumbnail styles */
.product-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: transform 0.2s ease;
}

.product-thumbnail:hover {
    transform: scale(1.1);
    cursor: pointer;
}

/* No image placeholder */
.no-image-placeholder {
    width: 60px;
    height: 60px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: #6c757d;
    border-radius: 4px;
    text-align: center;
}

.no-image-placeholder i {
    font-size: 1.2rem;
    margin-bottom: 2px;
    color: #adb5bd;
}

.no-image-placeholder span {
    font-size: 0.65rem;
}

/* Enhanced Admin Forms */
.admin-form {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group input[type="file"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    background: #ffffff;
    color: #111827;
    transition: all 0.2s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: #ffffff;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Enhanced Buttons */
.button,
.add-new-btn,
.back-btn,
.primary-btn,
.secondary-btn {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    border: 2px solid transparent;
    font-family: inherit;
}

.button,
.add-new-btn,
.primary-btn {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.back-btn,
.secondary-btn {
    background: white;
    color: #374151;
    border-color: #d1d5db;
}

.button:hover,
.add-new-btn:hover,
.primary-btn:hover {
    background: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.back-btn:hover,
.secondary-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
}

.button i,
.add-new-btn i,
.primary-btn i,
.back-btn i,
.secondary-btn i {
    font-size: 0.9rem;
}

/* Tables */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid #e5e5e5;
    text-align: left;
}

.admin-table th {
    background: #f8f8f8;
    font-weight: 600;
}

table td {
    vertical-align: top;
    padding: 1rem 0.75rem;
}

.table-responsive {
    overflow-x: auto;
    margin-top: 2rem;
}

/* Action Buttons */
.actions {
    display: flex;
    gap: 0.5rem;
}

.edit-btn,
.delete-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-btn {
    background: #f8f8f8;
    color: #000;
}

.delete-btn {
    background: #fff0f0;
    color: #dc3545;
}

.edit-btn:hover {
    background: #e5e5e5;
}

.delete-btn:hover {
    background: #ffe5e5;
}

/* Product Image Preview */
.product-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

/* Enhanced Messages */
.error-message,
.success-message {
    padding: 1.25rem 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-message {
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: #dc2626;
    border: 1px solid #fecaca;
}

.success-message {
    background: linear-gradient(135deg, #f0fdf4, #dcfce7);
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.error-message::before {
    content: "⚠️";
    font-size: 1.2rem;
}

.success-message::before {
    content: "✅";
    font-size: 1.2rem;
}

/* Product Variants */
.variants-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: #f8f8f8;
    border-radius: 8px;
}

.variant-row {
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.variant-row .form-row {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.variant-row .form-group {
    flex: 1;
}

.remove-variant-btn {
    padding: 0.5rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    height: 38px;
    width: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.add-variant-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    margin-top: 1rem;
}

.add-variant-btn:hover {
    background: #218838;
}

.remove-variant-btn:hover {
    background: #c82333;
}

.variants-list {
    margin-top: 0.5rem;
    font-size: 0.85em;
    color: #666;
}

.variants-list small {
    display: block;
    line-height: 1.4;
}

/* Images Section */
.images-section {
    margin: 2rem 0;
}

.current-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.image-preview {
    padding: 1rem;
    background: white;
    border-radius: 4px;
    text-align: center;
}

.image-preview img {
    width: 100%;
    height: 150px;
    object-fit: contain;
    margin-bottom: 0.5rem;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.stat-card i {
    font-size: 2rem;
    color: #000;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Supplement Form Fields */
.supplement-details {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
}

.supplement-details .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.supplement-details .form-group {
    flex: 1;
}

.supplement-details input[type="number"] {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

#supplement_category {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

/* Add some spacing between form sections */
.form-row + .form-row {
    margin-top: 1rem;
}

/* Style for the weight unit select */
#weight_unit {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

/* Enhanced Filter Section */
.filter-section {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
}

.filter-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.filter-group {
    flex: 1;
    min-width: 220px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    background: #ffffff;
    color: #111827;
    font-size: 1rem;
    transition: all 0.2s ease;
    font-family: inherit;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-group input::placeholder {
    color: #9ca3af;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.button-secondary {
    background: white;
    color: #374151;
    border: 2px solid #d1d5db;
}

.button-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

/* Enhanced Form Sections */
.form-section {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 12px;
    margin: 2rem 0;
    border: 1px solid #e2e8f0;
    position: relative;
}

.form-section h3 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #1e293b;
    font-weight: 600;
    font-size: 1.2rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
}

.form-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-row .form-group {
    flex: 1;
}

#supplementDetails input[type="number"],
#supplementDetails select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    background: #ffffff;
    color: #111827;
    font-size: 1rem;
    transition: all 0.2s ease;
}

#supplementDetails input[type="number"]:focus,
#supplementDetails select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#category {
    width: 100%;
    padding: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    background: #ffffff;
    color: #111827;
    margin-bottom: 0rem;
    font-size: 1rem;
    transition: all 0.2s ease;
}

#category:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e5e5e5;
    }

    .admin-main {
        padding: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

/* Best Seller Toggle Button */
.best-seller-toggle {
    text-align: center;
}

.toggle-form {
    margin: 0;
    display: inline-block;
}

.toggle-btn {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    color: #6c757d;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
    min-width: 100px;
    justify-content: center;
}

.toggle-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.toggle-btn.active {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-color: #ffc107;
    color: #856404;
    box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.toggle-btn.active:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}

.toggle-btn .fas {
    font-size: 0.9rem;
}

.toggle-btn.active .fas {
    color: #ff6b35;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Enhanced Input Styling */
input[type="text"]:hover,
input[type="email"]:hover,
input[type="password"]:hover,
input[type="number"]:hover,
input[type="file"]:hover,
select:hover,
textarea:hover {
    border-color: #9ca3af;
}

/* File Input Styling */
input[type="file"] {
    padding: 0.75rem;
    border: 2px dashed #d1d5db !important;
    background: #f9fafb;
    cursor: pointer;
}

input[type="file"]:hover {
    border-color: #3b82f6 !important;
    background: #eff6ff;
}

/* Checkbox and Radio Styling */
input[type="checkbox"],
input[type="radio"] {
    width: auto !important;
    margin-right: 0.5rem;
    transform: scale(1.2);
    accent-color: #3b82f6;
}

/* Select Dropdown Arrow */
select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
    text-decoration: none;
    border: 2px solid #333;
}

/* Required Field Indicator */
.form-group label[for] {
    position: relative;
}

.form-group label[for]:after {
    content: "";
}

.form-group input[required] + label:after,
.form-group textarea[required] + label:after,
label[for]:has(+ input[required]):after,
label[for]:has(+ textarea[required]):after {
    content: " *";
    color: #ef4444;
    font-weight: bold;
}

/* Form Validation States */
.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #ef4444;
    background-color: #fef2f2;
}

.form-group input.success,
.form-group select.success,
.form-group textarea.success {
    border-color: #10b981;
    background-color: #f0fdf4;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .toggle-btn {
        padding: 6px 10px;
        font-size: 0.8rem;
        min-width: 80px;
    }

    .toggle-btn span {
        display: none;
    }

    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    .admin-form {
        padding: 1.5rem;
    }

    .filter-section {
        padding: 1.5rem;
    }
}

/* ===== ENHANCED DASHBOARD STYLES ===== */

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2.5rem;
    border-radius: 16px;
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.welcome-section {
    flex: 1;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dashboard-title i {
    font-size: 2rem;
    opacity: 0.9;
}

.dashboard-subtitle {
    font-size: 1.1rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 300;
}

.dashboard-date {
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    backdrop-filter: blur(10px);
}

/* Enhanced Statistics Cards */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid #f1f5f9;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.stat-card.products {
    --card-color: #3b82f6;
    --card-color-light: #60a5fa;
}

.stat-card.categories {
    --card-color: #10b981;
    --card-color-light: #34d399;
}

.stat-card.blogs {
    --card-color: #f59e0b;
    --card-color-light: #fbbf24;
}

.stat-card.images {
    --card-color: #ef4444;
    --card-color-light: #f87171;
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    color: #1f2937;
}

.stat-content p {
    font-size: 1rem;
    color: #6b7280;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.stat-trend {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--card-color);
    font-weight: 600;
    background: rgba(59, 130, 246, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

/* Dashboard Grid Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.card-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid #f1f5f9;
}

.card-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: #1f2937;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-header h2 i {
    color: #3b82f6;
}

.card-header p {
    margin: 0;
    color: #6b7280;
    font-size: 0.95rem;
}

/* Quick Actions Grid */
.action-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 2rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem 1rem;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    text-decoration: none;
    color: #374151;
    transition: all 0.3s ease;
    font-weight: 500;
}

.action-btn:hover {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.action-btn i {
    font-size: 2rem;
    opacity: 0.8;
}

.action-btn span {
    font-size: 0.95rem;
}

/* Activity List */
.activity-list {
    padding: 2rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.activity-icon.blog {
    background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.activity-icon.info {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    color: #374151;
    font-size: 0.95rem;
}

.activity-time {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Status List */
.status-list {
    padding: 2rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.status-item:last-child {
    border-bottom: none;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-indicator.online {
    background: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.status-item span {
    flex: 1;
    color: #374151;
    font-weight: 500;
}

.status-check {
    color: #10b981;
    font-size: 1.1rem;
}

/* Dashboard Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 2rem 1.5rem;
    }

    .dashboard-title {
        font-size: 2rem;
        justify-content: center;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .action-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
        gap: 1rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-content h3 {
        font-size: 2rem;
    }

    .dashboard-card {
        margin-bottom: 1rem;
    }

    .card-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
    }

    .activity-list,
    .status-list {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 1.5rem 1rem;
    }

    .dashboard-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .dashboard-subtitle {
        font-size: 1rem;
    }

    .stat-card {
        padding: 1.25rem;
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-content h3 {
        font-size: 1.75rem;
    }

    .action-btn {
        padding: 1.5rem 1rem;
    }

    .action-btn i {
        font-size: 1.5rem;
    }
}

/* Dashboard Animation Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-stats .stat-card {
    animation: fadeInUp 0.6s ease forwards;
}

.dashboard-stats .stat-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-stats .stat-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-stats .stat-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-stats .stat-card:nth-child(4) { animation-delay: 0.4s; }

.dashboard-card {
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.5s;
}

/* Dashboard Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    .dashboard-header {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    }

    .stat-card,
    .dashboard-card {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }

    .stat-content h3 {
        color: #f9fafb;
    }

    .activity-content p {
        color: #d1d5db;
    }

    .status-item span {
        color: #d1d5db;
    }
}
