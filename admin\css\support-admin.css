/* Support Admin Panel Styles */

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Settings Navigation */
.settings-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.settings-nav .nav-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background: white;
    color: #333;
    text-decoration: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.settings-nav .nav-link:hover,
.settings-nav .nav-link.active {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.settings-nav .nav-link.active {
    background: #28a745;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.settings-nav .nav-link i {
    font-size: 0.8rem;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0,123,255,0.3);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,123,255,0.4);
}

/* Section Anchors */
.settings-section {
    scroll-margin-top: 20px;
}

.settings-section h2,
.settings-section h3 {
    scroll-margin-top: 20px;
}

/* Dashboard Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 15px;
    color: #007bff;
}

.stat-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #666;
    font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-actions h2 {
    margin-bottom: 20px;
    color: #333;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.action-buttons .btn {
    padding: 12px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Settings Form */
.settings-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.settings-section h2 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 10px;
}

.settings-form .form-group {
    margin-bottom: 20px;
}

.settings-form label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.settings-form .form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: border-color 0.2s ease;
}

.settings-form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-check {
    padding: 10px 0;
}

.form-check-input {
    margin-right: 10px;
}

.form-check-label {
    font-weight: 500;
    color: #333;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #f8f9fa;
}

.form-actions .btn {
    margin-right: 15px;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 500;
}

/* Alerts */
.alert {
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    border: none;
}

.alert-info {
    background-color: #e3f2fd;
    color: #0277bd;
}

.alert-success {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.alert-warning {
    background-color: #fff3e0;
    color: #f57c00;
}

.alert-danger {
    background-color: #ffebee;
    color: #c62828;
}

/* Tables */
.table-responsive {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #333;
    padding: 15px;
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #f8f9fa;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Status Badges */
.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-open {
    background-color: #fff3cd;
    color: #856404;
}

.status-in_progress {
    background-color: #cce5ff;
    color: #004085;
}

.status-pending_customer {
    background-color: #f8d7da;
    color: #721c24;
}

.status-resolved {
    background-color: #d4edda;
    color: #155724;
}

.status-closed {
    background-color: #e2e3e5;
    color: #383d41;
}

/* Priority Badges */
.priority-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-low {
    background-color: #d4edda;
    color: #155724;
}

.priority-medium {
    background-color: #fff3cd;
    color: #856404;
}

.priority-high {
    background-color: #f8d7da;
    color: #721c24;
}

.priority-urgent {
    background-color: #f5c6cb;
    color: #721c24;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Ticket Numbers */
.ticket-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
}

/* Webhook Info */
.webhook-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.webhook-info table {
    margin-bottom: 0;
}

.webhook-info td {
    padding: 8px 12px;
}

/* Templates Section */
.templates-section {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.templates-section h2 {
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.templates-section .badge {
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .action-buttons .btn {
        width: 100%;
        text-align: center;
    }

    .settings-nav {
        flex-direction: column;
        gap: 8px;
    }

    .settings-nav .nav-link {
        justify-content: center;
        padding: 12px 15px;
    }

    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .form-row {
        flex-direction: column;
    }

    .form-row .form-group {
        width: 100%;
    }

    .settings-section {
        padding: 20px;
        margin-bottom: 20px;
    }
}

/* Smooth scroll for mobile */
@media (max-width: 768px) {
    html {
        scroll-padding-top: 60px;
    }

    .settings-section {
        scroll-margin-top: 60px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .stat-card,
    .quick-actions,
    .settings-section,
    .templates-section {
        background: #2d3748;
        color: #e2e8f0;
    }

    .stat-content h3,
    .quick-actions h2,
    .settings-section h2,
    .templates-section h2 {
        color: #e2e8f0;
    }

    .form-control {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }

    .table thead th {
        background-color: #4a5568;
        color: #e2e8f0;
    }

    .table tbody tr:hover {
        background-color: #4a5568;
    }

    .settings-nav {
        background: rgba(45, 55, 72, 0.3);
    }

    .settings-nav .nav-link {
        background: #4a5568;
        color: #e2e8f0;
    }
}