/* Alpha Nutrition OMS Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

/* OMS Container */
.oms-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.oms-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 4px 0 20px rgba(0,0,0,0.1);
}

.oms-logo {
    padding: 30px 25px;
    text-align: center;
    border-bottom: 1px solid #404040;
    background: rgba(255,255,255,0.05);
}

.oms-logo i {
    font-size: 2.5rem;
    color: #4ECDC4;
    margin-bottom: 10px;
}

.oms-logo h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.oms-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 25px;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.nav-item:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-left-color: #4ECDC4;
}

.nav-item.active {
    background: rgba(78, 205, 196, 0.2);
    color: white;
    border-left-color: #4ECDC4;
}

.nav-item.logout {
    margin-top: 30px;
    border-top: 1px solid #404040;
    padding-top: 25px;
    color: #ff6b6b;
}

.nav-item.logout:hover {
    background: rgba(255, 107, 107, 0.2);
    border-left-color: #ff6b6b;
}

.nav-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* Main Content */
.oms-main {
    flex: 1;
    margin-left: 280px;
    padding: 30px;
    background: #f8f9fa;
    min-height: 100vh;
}

.oms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.oms-header h1 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #333;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.admin-profile i {
    font-size: 1.5rem;
    color: #4ECDC4;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.25);
    border-color: #d0d0d0;
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
}

.stat-icon.total { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.stat-icon.today { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.stat-icon.pending { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; }
.stat-icon.revenue { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333; }

.stat-info h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-info p {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 8px;
}

.stat-change {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
}

.stat-change.positive {
    background: #d4edda;
    color: #155724;
}

.stat-change.negative {
    background: #f8d7da;
    color: #721c24;
}

.stat-change.neutral {
    background: #f3f4f6;
    color: #374151;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.card-header {
    padding: 20px 25px;
    border-bottom: 1px solid #d0d0d0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
}

.card-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
}

.card-content {
    padding: 25px;
}

/* Recent Orders */
.recent-orders {
    max-height: 400px;
    overflow-y: auto;
}

.order-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.order-item:last-child {
    border-bottom: none;
}

.order-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.order-info p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.order-info small {
    color: #adb5bd;
    font-size: 0.8rem;
}

.order-status {
    text-align: center;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #f3f4f6;
    color: #374151;
}

.status-badge.confirmed {
    background: #d4edda;
    color: #155724;
}

.status-badge.processing {
    background: #cce5ff;
    color: #004085;
}

.status-badge.shipped {
    background: #e2e3e5;
    color: #383d41;
}

.status-badge.delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
}

.order-amount {
    font-weight: 600;
    color: #333;
    margin-top: 5px;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    border-left: 4px solid #4ECDC4;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.25);
    border-color: #d0d0d0;
    color: inherit;
}

.action-card i {
    font-size: 2rem;
    color: #4ECDC4;
    margin-bottom: 15px;
}

.action-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.action-card p {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: #4ECDC4;
    color: white;
}

.btn-primary:hover {
    background: #45b7aa;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-xs {
    padding: 4px 8px;
    font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .oms-sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }
    
    .oms-main {
        margin-left: 0;
        padding: 20px 15px;
    }
    
    .oms-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1000px;
    width: 100%;
    margin: 20px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    overflow: hidden;
}

.login-card {
    padding: 50px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.login-logo {
    margin-bottom: 20px;
}

.login-logo i {
    font-size: 3rem;
    color: #4ECDC4;
    margin-bottom: 10px;
}

.login-logo h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.login-header h2 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 8px;
}

.login-header p {
    color: #6c757d;
    font-size: 0.95rem;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.alert-error {
    background: #fff5f5;
    color: #e53e3e;
    border: 1px solid #fed7d7;
}

.alert-success {
    background: #f0fff4;
    color: #38a169;
    border: 1px solid #c6f6d5;
}

.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 0.95rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 15px;
    color: #6c757d;
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #4ECDC4;
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.password-toggle {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    z-index: 2;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e9ecef;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #4ECDC4;
    border-color: #4ECDC4;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-link {
    color: #4ECDC4;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: #45b7aa;
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #4ECDC4 0%, #45b7aa 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.login-footer {
    text-align: center;
}

.security-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 15px;
    color: #28a745;
    font-size: 0.9rem;
    font-weight: 500;
}

.support-info p {
    color: #6c757d;
    font-size: 0.85rem;
}

.login-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 50px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    background-size: 50px 50px;
}

.feature-list {
    position: relative;
    z-index: 2;
}

.feature-list h3 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
}

.feature-list ul {
    list-style: none;
    padding: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.feature-list li i {
    color: #4ECDC4;
    font-size: 1.2rem;
}

/* Mobile Login Responsive */
@media (max-width: 768px) {
    .login-container {
        grid-template-columns: 1fr;
        margin: 10px;
        border-radius: 15px;
    }

    .login-card {
        padding: 30px 25px;
    }

    .login-bg {
        padding: 30px 25px;
        order: -1;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}

/* Settings Page Styles */
.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.settings-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.settings-card.full-width {
    grid-column: 1 / -1;
}

.settings-form {
    max-width: none;
}

.settings-form .form-group {
    margin-bottom: 20px;
}

.settings-form label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.settings-form input,
.settings-form select,
.settings-form textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.settings-form input:focus,
.settings-form select:focus,
.settings-form textarea:focus {
    outline: none;
    border-color: #4ECDC4;
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.settings-form input:disabled {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

.form-help {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
    display: block;
}

.password-input {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input input {
    padding-right: 45px;
}

.password-toggle {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #333;
}

.settings-grid-inner {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4ECDC4;
}

.setting-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
}

.setting-info p {
    margin: 0;
    color: #6c757d;
    font-size: 0.85rem;
}

.setting-control {
    min-width: 120px;
    text-align: right;
}

.setting-control input {
    width: auto;
    max-width: 120px;
    margin: 0;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4ECDC4;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

input:disabled + .slider {
    opacity: 0.5;
    cursor: not-allowed;
}

.settings-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Orders Table Styles */
.filters-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    margin-bottom: 30px;
}

.filters-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group input,
.filter-group select {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #333;
    border-radius: 6px;
    font-size: 0.9rem;
    text-decoration: none;
}

.filter-group select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
    overflow: hidden;
    margin-bottom: 30px;
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th,
.orders-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #f1f3f4;
}

.orders-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.orders-table tbody tr:hover {
    background: #f8f9fa;
}

.customer-info strong {
    color: #333;
    font-size: 0.95rem;
}

.customer-info small {
    color: #6c757d;
    font-size: 0.8rem;
}

.delivery-info strong {
    color: #333;
    font-size: 0.9rem;
}

.delivery-status {
    font-size: 0.8rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.delivery-status.assigned {
    background: #f3f4f6;
    color: #374151;
}

.delivery-status.picked_up {
    background: #cce5ff;
    color: #004085;
}

.delivery-status.in_transit {
    background: #e2e3e5;
    color: #383d41;
}

.delivery-status.delivered {
    background: #d1ecf1;
    color: #0c5460;
}

.text-muted {
    color: #6c757d;
    font-style: italic;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    border: 1px solid #e0e0e0;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* Mobile responsive for settings and orders */
@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .setting-control {
        width: 100%;
        text-align: left;
    }

    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .orders-table {
        font-size: 0.85rem;
    }

    .orders-table th,
    .orders-table td {
        padding: 10px 8px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }

    .pagination {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* Order Details Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.large-modal {
    max-width: 900px;
    width: 90%;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
    padding: 0;
    background: none;
    border: none;
}

.close:hover,
.close:focus {
    color: #333;
}

.modal-body {
    padding: 25px;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.error-message {
    text-align: center;
    padding: 40px;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 8px;
    margin: 20px 0;
}

/* Order Details Content Styles */
.order-details-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.order-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.summary-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.summary-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-item span {
    color: #333;
    font-size: 1rem;
}

.order-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
}

.customer-details,
.order-items,
.payment-info,
.order-notes {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

.customer-details h4,
.order-items h4,
.payment-info h4,
.order-notes h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: #6c757d;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    color: #333;
    font-size: 0.95rem;
}

.items-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.item-row {
    display: grid;
    grid-template-columns: 2fr auto auto auto;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.item-info strong {
    color: #333;
    font-size: 0.95rem;
}

.item-info small {
    color: #6c757d;
    font-size: 0.8rem;
}

.item-quantity,
.item-price,
.item-total {
    text-align: right;
    font-weight: 600;
    color: #333;
}

.order-totals {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.total-row:last-child {
    border-bottom: none;
}

.total-row.final-total {
    font-weight: bold;
    font-size: 1.1rem;
    color: #333;
    border-top: 2px solid #333;
    margin-top: 10px;
    padding-top: 15px;
}

.payment-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.payment-badge.pending {
    background: #f3f4f6;
    color: #374151;
}

.payment-badge.paid {
    background: #d4edda;
    color: #155724;
}

.payment-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

.order-notes p {
    margin: 0;
    color: #333;
    line-height: 1.6;
}

/* Mobile responsive for modal */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 85vh;
    }

    .modal-header,
    .modal-body {
        padding: 15px;
    }

    .summary-row,
    .details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .item-row {
        grid-template-columns: 1fr;
        gap: 10px;
        text-align: left;
    }

    .item-quantity,
    .item-price,
    .item-total {
        text-align: left;
    }
}

/* Loader Overlay */
.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loader {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    color: #333;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.loader i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
    color: #007bff;
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    min-width: 300px;
    border-left: 4px solid #007bff;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left-color: #28a745;
}

.notification-success i {
    color: #28a745;
}

.notification-error {
    border-left-color: #dc3545;
}

.notification-error i {
    color: #dc3545;
}

.notification-info {
    border-left-color: #007bff;
}

.notification-info i {
    color: #007bff;
}

.notification-close {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0.25rem;
    margin-left: auto;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notification-close:hover {
    background-color: #f8f9fa;
}

/* Enhanced Button Styles */
.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

/* Maintenance Actions */
.maintenance-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.maintenance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border: 2px solid #333;
    border-radius: 8px;
    background: #f8f9fa;
}

.maintenance-info h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
    font-weight: 600;
}

.maintenance-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.maintenance-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Mobile responsive for notifications */
@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }

    .maintenance-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .maintenance-controls {
        width: 100%;
        justify-content: flex-start;
    }
}
